{"name": "realtime-yjs-server", "version": "1.0.0", "description": "A real-time collaborative server using YJS and Y-socket with SOLID principles", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "docker:build": "docker build -t realtime-yjs-server .", "docker:run": "docker run -p 3000:3000 realtime-yjs-server"}, "keywords": ["yjs", "y-socket", "realtime", "collaboration", "websocket", "nodejs"], "author": "", "license": "MIT", "dependencies": {"y-socket.io": "^1.0.2", "yjs": "^13.6.10", "socket.io": "^4.7.5", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}