# Realtime YJS Server

A production-ready, real-time collaborative server built with YJS and Socket.IO, following SOLID principles and best practices.

## Features

- 🚀 **Real-time Collaboration**: Built on YJS for conflict-free replicated data types (CRDTs)
- 🏗️ **SOLID Architecture**: Clean, maintainable code following SOLID principles
- 🐳 **Docker Ready**: Fully containerized with Docker and docker-compose
- 📊 **Monitoring**: Built-in health checks and statistics endpoints
- 🔒 **Security**: Helmet.js, CORS, rate limiting, and non-root Docker user
- 📝 **Logging**: Comprehensive logging with Winston
- ⚡ **Performance**: Efficient connection and document management
- 🔄 **Graceful Shutdown**: Proper cleanup and client notification

## Architecture

The server follows SOLID principles with a clean separation of concerns:

```
src/
├── config/          # Configuration management
├── interfaces/      # Interface definitions (ISP)
├── managers/        # Core business logic managers
├── handlers/        # WebSocket event handlers
├── services/        # High-level service orchestration
├── server/          # Express server setup
├── middleware/      # Error handling middleware
└── utils/           # Utility classes (Logger)
```

### SOLID Principles Implementation

- **Single Responsibility**: Each class has one reason to change
- **Open/Closed**: Extensible without modification
- **Liskov Substitution**: Interfaces ensure substitutability
- **Interface Segregation**: Focused, specific interfaces
- **Dependency Inversion**: Depends on abstractions, not concretions

## Quick Start

### Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd realtime-yjs-server
   cp .env.example .env
   ```

2. **Run with Docker Compose**:
   ```bash
   docker-compose up -d
   ```

3. **Check health**:
   ```bash
   curl http://localhost:3000/health
   ```

### Manual Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start the server**:
   ```bash
   npm start
   # or for development
   npm run dev
   ```

## Configuration

Environment variables (see `.env.example`):

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | 3000 | Server port |
| `HOST` | 0.0.0.0 | Server host |
| `NODE_ENV` | development | Environment |
| `CORS_ORIGIN` | * | CORS origin |
| `LOG_LEVEL` | info | Logging level |
| `YJS_PERSISTENCE` | false | Enable YJS persistence |
| `YJS_GC_ENABLED` | true | Enable garbage collection |
| `WS_PING_TIMEOUT` | 60000 | WebSocket ping timeout |
| `WS_PING_INTERVAL` | 25000 | WebSocket ping interval |

## API Endpoints

### Health Check
```
GET /health
```

### Statistics
```
GET /api/stats
```

### Document Information
```
GET /api/documents/:documentId
```

### Force Document Cleanup
```
DELETE /api/documents/:documentId
```

## WebSocket Events

### Client to Server

- `join-document`: Join a document room
- `yjs-update`: Send YJS document updates
- `awareness-update`: Send awareness information (cursors, selections)
- `sync-request`: Request document synchronization

### Server to Client

- `joined-document`: Confirmation of document join
- `yjs-update`: YJS document updates from other clients
- `awareness-update`: Awareness updates from other clients
- `sync-response`: Document synchronization response
- `user-joined`: Notification of new user
- `user-left`: Notification of user leaving
- `server-shutdown`: Server shutdown notification
- `error`: Error messages

## Client Integration

### Basic JavaScript Client

```javascript
import { io } from 'socket.io-client';
import * as Y from 'yjs';

// Connect to server
const socket = io('http://localhost:3000');

// Create YJS document
const doc = new Y.Doc();

// Join document
socket.emit('join-document', {
  documentId: 'my-document',
  userId: 'user-123'
});

// Handle document updates
doc.on('update', (update, origin) => {
  if (origin !== socket.id) {
    socket.emit('yjs-update', {
      documentId: 'my-document',
      update: Array.from(update)
    });
  }
});

// Handle updates from server
socket.on('yjs-update', (data) => {
  if (data.origin !== socket.id) {
    const update = new Uint8Array(data.update);
    Y.applyUpdate(doc, update, socket.id);
  }
});

// Handle sync response
socket.on('sync-response', (data) => {
  const state = new Uint8Array(data.state);
  Y.applyUpdate(doc, state, socket.id);
});
```

### React Hook Example

```javascript
import { useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import * as Y from 'yjs';

export function useYjsDocument(documentId, userId) {
  const [doc] = useState(() => new Y.Doc());
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    const newSocket = io('http://localhost:3000');
    
    newSocket.on('connect', () => {
      setConnected(true);
      newSocket.emit('join-document', { documentId, userId });
    });

    newSocket.on('disconnect', () => {
      setConnected(false);
    });

    newSocket.on('yjs-update', (data) => {
      if (data.origin !== newSocket.id) {
        const update = new Uint8Array(data.update);
        Y.applyUpdate(doc, update, newSocket.id);
      }
    });

    newSocket.on('sync-response', (data) => {
      const state = new Uint8Array(data.state);
      Y.applyUpdate(doc, state, newSocket.id);
    });

    doc.on('update', (update, origin) => {
      if (origin !== newSocket.id && connected) {
        newSocket.emit('yjs-update', {
          documentId,
          update: Array.from(update)
        });
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [documentId, userId, doc, connected]);

  return { doc, socket, connected };
}
```

## Production Deployment

### With Nginx (Recommended)

1. **Enable nginx profile**:
   ```bash
   docker-compose --profile production up -d
   ```

2. **Configure SSL** (update `nginx.conf`):
   - Add your SSL certificates to `./ssl/`
   - Uncomment HTTPS server block
   - Update server name

### Environment Considerations

- Set `NODE_ENV=production`
- Configure proper CORS origins
- Set up SSL/TLS certificates
- Configure log rotation
- Set up monitoring and alerting
- Consider using a process manager like PM2

## Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Statistics
```bash
curl http://localhost:3000/api/stats
```

### Docker Health Check
```bash
docker ps  # Check container health status
```

## Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

### Docker Development
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow SOLID principles
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the example implementations
