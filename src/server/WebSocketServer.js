const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const helmet = require('helmet');
const { setupWSConnection } = require('../utils/y-websocket-utils');

/**
 * WebSocket Server Class
 * Combines Express HTTP server with native WebSocket server for y-websocket
 * Follows Single Responsibility Principle - handles HTTP and WebSocket server setup
 * Follows Open/Closed Principle - extensible for additional middleware
 */
class WebSocketServer {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.isRunning = false;
    this.yjsService = null;
  }

  /**
   * Initialize the Express server with middleware
   */
  initialize() {
    try {
      // Security middleware
      this.app.use(helmet({
        contentSecurityPolicy: false, // Allow WebSocket connections
        crossOriginEmbedderPolicy: false
      }));

      // CORS middleware
      this.app.use(cors(this.config.get('cors')));

      // Body parsing middleware
      this.app.use(express.json({ limit: '10mb' }));
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

      // Request logging middleware
      this.app.use((req, res, next) => {
        this.logger.http(`${req.method} ${req.url}`, {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        next();
      });

      // Serve static files for examples and libraries
      this.app.use('/examples', express.static('examples'));
      this.app.use('/public', express.static('public'));

      // Health check endpoint
      this.app.get('/health', (req, res) => {
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          websocket: this.wss ? 'active' : 'inactive'
        });
      });

      // API routes
      this.setupApiRoutes();

      // Error handling middleware
      this.setupErrorHandling();

      this.logger.info('Express server initialized');
    } catch (error) {
      this.logger.error('Failed to initialize Express server', error);
      throw error;
    }
  }

  /**
   * Setup API routes
   */
  setupApiRoutes() {
    const apiRouter = express.Router();

    // Get server statistics
    apiRouter.get('/stats', (req, res) => {
      try {
        if (this.yjsService) {
          const stats = this.yjsService.getStats();
          res.json(stats);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get stats', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get document information
    apiRouter.get('/documents/:documentId', (req, res) => {
      try {
        const { documentId } = req.params;
        if (this.yjsService) {
          const info = this.yjsService.getDocumentInfo(documentId);
          res.json(info);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get document info', error, {
          documentId: req.params.documentId
        });
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Force document cleanup (admin endpoint)
    apiRouter.delete('/documents/:documentId', (req, res) => {
      try {
        const { documentId } = req.params;
        if (this.yjsService) {
          const removed = this.yjsService.cleanupDocument(documentId);
          res.json({ removed, documentId });
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to cleanup document', error, {
          documentId: req.params.documentId
        });
        res.status(400).json({ error: error.message });
      }
    });

    this.app.use('/api', apiRouter);
  }

  /**
   * Setup error handling middleware
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.url} not found`
      });
    });

    // Global error handler
    this.app.use((error, req, res, next) => {
      this.logger.error('Unhandled error in Express', error, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  /**
   * Initialize WebSocket server for y-websocket
   */
  initializeWebSocket() {
    try {
      // Create WebSocket server without a server (we'll handle upgrade manually)
      this.wss = new WebSocket.Server({ noServer: true });

      // Handle WebSocket connections using y-websocket setup
      this.wss.on('connection', (ws, req) => {
        const url = req.url || '';
        const documentId = url.slice(1).split('?')[0] || 'default';
        const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        this.logger.info('New WebSocket connection', {
          connectionId,
          url: req.url,
          documentId,
          origin: req.headers.origin
        });

        // Extract user ID from query parameters if available
        const urlParams = new URLSearchParams(url.split('?')[1] || '');
        const userId = urlParams.get('userId') || `user-${Math.random().toString(36).substr(2, 9)}`;

        // Add connection to our ConnectionManager if YJS service is available
        if (this.yjsService && this.yjsService.connectionManager) {
          try {
            this.yjsService.connectionManager.addConnection(connectionId, ws, {
              documentId,
              userId,
              url: req.url,
              origin: req.headers.origin
            });
          } catch (error) {
            this.logger.error('Failed to add connection to ConnectionManager', error, {
              connectionId,
              documentId
            });
          }
        }

        // Handle connection close
        ws.on('close', () => {
          this.logger.info('WebSocket connection closed', {
            connectionId,
            documentId,
            userId
          });

          // Remove connection from our ConnectionManager
          if (this.yjsService && this.yjsService.connectionManager) {
            try {
              this.yjsService.connectionManager.removeConnection(connectionId);
            } catch (error) {
              this.logger.error('Failed to remove connection from ConnectionManager', error, {
                connectionId
              });
            }
          }
        });

        // Use y-websocket connection setup
        setupWSConnection(ws, req);
      });

      // Handle HTTP upgrade requests for WebSocket
      this.server.on('upgrade', (request, socket, head) => {
        // You can add authentication/authorization here
        // For now, we'll allow all connections
        this.wss.handleUpgrade(request, socket, head, (ws) => {
          this.wss.emit('connection', ws, request);
        });
      });

      this.logger.info('WebSocket server initialized');
      return this.wss;
    } catch (error) {
      this.logger.error('Failed to initialize WebSocket server', error);
      throw error;
    }
  }

  /**
   * Set YJS service reference for API endpoints
   */
  setYjsService(yjsService) {
    this.yjsService = yjsService;
  }

  /**
   * Start the server
   */
  async start() {
    try {
      const port = this.config.get('port');
      const host = this.config.get('host');

      await new Promise((resolve, reject) => {
        this.server.listen(port, host, (error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            this.logger.info(`Server started on ${host}:${port}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  /**
   * Stop the server
   */
  async stop() {
    try {
      if (!this.isRunning) {
        return;
      }

      // Close WebSocket server first
      if (this.wss) {
        this.wss.close();
      }

      await new Promise((resolve) => {
        this.server.close(() => {
          this.isRunning = false;
          this.logger.info('Server stopped');
          resolve();
        });
      });
    } catch (error) {
      this.logger.error('Failed to stop server', error);
      throw error;
    }
  }

  /**
   * Get WebSocket server instance
   */
  getWebSocketServer() {
    return this.wss;
  }

  /**
   * Check if server is running
   */
  isServerRunning() {
    return this.isRunning;
  }
}

module.exports = WebSocketServer;
